###########短信网关配置###################
#########189短信平台配置##############
cloudkey.system.sms_189_send_message_url=http://api.189.cn/v2/emp/templateSms/sendSms
cloudkey.system.sms_189_token_url=https://oauth.api.189.cn/emp/oauth2/v3/access_token
#189短信账号密码
cloudkey.system.sms_189_app_id=330669470000276318
cloudkey.system.sms_189_app_secret=559b39c779147d8d4be202b3022c4acc
#发送用户pin模板
cloudkey.system.sms_189_user_pin_template_id=91555185
# 解锁成功通知用户
cloudkey.system.sms_189_unlock_success_template_id=91555200
#发送短信验证码模板
cloudkey.system.sms_189_valid_code_template_id=91553108
#证书签发成功之后通知用户短信模板
cloudkey.system.sms_189_notify_user_cert_success_template_id=91556894
#通知用户授权短信模板id
cloudkey.system.sms_189_notify_user_authorize_template_id=91555498
#授权成功，通知证书用户
cloudkey.system.sms_189_notify_user_authorized_success_template_id=91555499
#发送被授权人pin
cloudkey.system.sms_189_send_authorize_pin_template_id=91555500
#修改授权通知被授权人模板id
cloudkey.system.sms_189_send_authorize_modify_template_id=91556162
#########189短信平台配置##############

###########短信网关配置（阿里云）###################
#API支持的RegionID
cloudkey.system.aliyun.RegionId=cn-hangzhou
#AccessKey ID
cloudkey.system.aliyun.accessKeyId=LTAI4G3sZcUZ1gRs6NFL5TJw
#AccessKey Secret
cloudkey.system.aliyun.secret=******************************
#sysDomain
cloudkey.system.aliyun.sysDomain=dysmsapi.aliyuncs.com
#API 的版本号
cloudkey.system.aliyun.sysVersion=2017-05-25
#发送短信API 的名称
cloudkey.system.aliyun.sysAction=SendSms
#短信签名名称
cloudkey.system.aliyun.SignName=网证通
#短信模板（括号内为阿里云配置的短信模板名）
#发送用户pin模板（用户PIN码）
cloudkey.system.aliyun_user_pin_template_code=SMS_209171783
# 解锁成功通知用户（密钥解锁）
cloudkey.system.aliyun_unlock_success_template_code=SMS_209192188
#发送短信验证码模板（用户操作验证码）
cloudkey.system.aliyun_valid_code_template_code=SMS_209196813
#证书签发成功之后通知用户短信模板（证书签发成功通知）
cloudkey.system.aliyun_notify_user_cert_success_template_code=SMS_211495448
#通知用户授权短信模板id（授权操作通知）
cloudkey.system.aliyun_notify_user_authorize_template_code=SMS_209192197
#授权成功，通知证书用户（授权操作成功通知APP）
cloudkey.system.aliyun_notify_user_authorized_success_template_code=SMS_209197162
#发送被授权人pin（被授权通知）
cloudkey.system.aliyun_send_authorize_pin_template_code=SMS_209172164
#修改授权通知被授权人模板id(授权次数通知)
cloudkey.system.aliyun_send_authorize_modify_template_code=SMS_209172490
###########短信网关配置（阿里云）###################
###########短信网关配置（网证通）###################
#短信url
cloudkey.system.netca.url=https://bpms.cnca.net/ia/v1/api/sendsms
#短信签名
cloudkey.system.netca.signName=【网证通】
#短信appId
cloudkey.system.netca.appId=cloudkey_app
#短信appKey
cloudkey.system.netca.appKey=6640193361039919
#短信类型
cloudkey.system.netca.smsType=1
#短信模板（括号内为阿里云配置的短信模板名）
#发送用户pin模板（用户PIN码）
cloudkey.system.netca_user_pin_template_code=SMS_209171783
# 解锁成功通知用户（密钥解锁）
cloudkey.system.netca_unlock_success_template_code=SMS_209192188
#发送短信验证码模板（用户操作验证码）
cloudkey.system.netca_valid_code_template_code=SMS_209196813
#证书签发成功之后通知用户短信模板（证书签发成功通知）
cloudkey.system.netca_notify_user_cert_success_template_code=SMS_209172156
#通知用户授权短信模板id（授权操作通知）
cloudkey.system.netca_notify_user_authorize_template_code=SMS_209192197
#授权成功，通知证书用户（授权操作成功通知APP）
cloudkey.system.netca_notify_user_authorized_success_template_code=SMS_209197162
#发送被授权人pin（被授权通知）
cloudkey.system.netca_send_authorize_pin_template_code=SMS_209172164
#修改授权通知被授权人模板id(授权次数通知)
cloudkey.system.netca_send_authorize_modify_template_code=SMS_209172490
###########短信网关配置（网证通）###################
# 证书过期预警（阿里云）
cloudkey.system.aliyun_send_cert_expiry_warning_code=SMS_465316647
# 证书过期预警（Netca）
cloudkey.system.netca_send_cert_expiry_warning_code=SMS_465316647
# APP设备绑定（阿里云）
cloudkey.system.aliyun_send_app_device_binding_template_code=SMS_468975616
# APP设备绑定（Netca）
cloudkey.system.netca_send_app_device_binding_template_code=SMS_468975616