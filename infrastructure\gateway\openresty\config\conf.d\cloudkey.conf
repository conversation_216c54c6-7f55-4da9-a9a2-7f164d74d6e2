# 按照官方文档配置upstream
upstream cloudkey_backend {
    # 定义后端服务器，使用文档推荐的参数
    server *************:8082;
    #server 192.168.200.138:8082;

    # 保持连接配置
    keepalive 1200;                    # 保持连接数
    #keepalive_requests 1000 ;          # 每个连接的请求数
    keepalive_timeout 135s;           # 连接超时时间
}

# the size depends on the number of servers in upstream {}:
#lua_shared_dict healthcheck 1m;
#lua_shared_dict health_check_log 5m;  # 存储最近的健康检查日志

#lua_socket_log_errors off;

#init_worker_by_lua_block {
#    local hc = require "resty.upstream.healthcheck"

#    local ok, err = hc.spawn_checker{
#        shm = "healthcheck",
#        upstream = "cloudkey_backend",
#        type = "http",
#        http_req = "GET /cloudkeyserver/system/setting/version HTTP/1.1\r\nHost: *************\r\n\r\n",
#        interval = 2000,
#        timeout = 10000,
#        fall = 3,
#        rise = 2,
#        valid_statuses = {200, 302},
#        concurrency = 10
#    }
#    if not ok then
#        ngx.log(ngx.ERR, "failed to spawn health checker: ", err)
#        return
#    end
#}

server {
    listen                  6443 ssl;
    server_name             in_ssl;

    keepalive_timeout 125s;  # 原配置70s导致服务端先关闭连接# 提高日志级别以便排查问题

    access_log              /usr/local/openresty/nginx/logs/access_cloudkey_6443.log timed_escape;
    error_log               /usr/local/openresty/nginx/logs/error_cloudkey_6443.log error;

    # SSL配置优化
    ssl_certificate         /etc/nginx/ssl/nginx.cer;
    ssl_certificate_key     /etc/nginx/ssl/nginx_cert.key;
    ssl_protocols           TLSv1.2 TLSv1.3;
    ssl_ciphers             HIGH:!aNULL:!MD5:!3DES;
    ssl_prefer_server_ciphers on;
    ssl_session_cache       shared:SSL:10m;
    ssl_session_timeout     10m;
    ssl_verify_client       off;
    # 禁用SSL Stapling，因为发行者证书不可用
    # ssl_stapling            on;
    # ssl_stapling_verify     on;

    # 缓冲配置优化
    client_header_buffer_size       4k;
    large_client_header_buffers     4 1m;
    client_max_body_size            100m;
    client_body_buffer_size         128k;

    # 安全相关的HTTP头
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # 全局CORS配置（允许跨域访问）- 在生产环境中建议限制为特定域名
    add_header Access-Control-Allow-Origin "*" always;
    add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
    add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization" always;
    add_header Access-Control-Allow-Credentials "true" always;
    add_header Access-Control-Max-Age "3600" always;

    # GZIP压缩配置
    gzip on;
    gzip_comp_level 6;
    gzip_min_length 1k;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    gzip_vary on;

    # 处理OPTIONS请求（预检请求）
    if ($request_method = 'OPTIONS') {
        return 204;
    }

    location /cloudkeyserver/ {
        # 直接使用Nginx的负载均衡机制，不再使用自定义的health_monitor.lua
        proxy_pass http://cloudkey_backend;

        # 代理头部设置

        proxy_connect_timeout 5s;
        proxy_send_timeout   20s;
        proxy_read_timeout   140s;


        proxy_set_header Host      $host:$server_port;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;

        # 错误处理
        proxy_intercept_errors on;

        # 缓存设置
        proxy_cache_bypass $http_cache_control;
        proxy_cache_valid 200 302 10m;
        proxy_cache_valid 404 1m;

        # 优化代理缓冲（高并发场景）
        proxy_buffering on;
        proxy_buffer_size 64k;         # 增大头缓冲区（原16k易溢出）
        proxy_buffers 8 64k;           # 总缓冲大小 = 8*64k = 512k
        proxy_busy_buffers_size 128k;  # 提升忙碌缓冲区上限


        # 连接升级支持（WebSocket）
        #proxy_http_version 1.1;
        #proxy_set_header Upgrade $http_upgrade;
        #proxy_set_header Connection "upgrade";
    }

    location = /status {
                access_log off;
                default_type text/plain;
                content_by_lua_block {
                    local hc = require "resty.upstream.healthcheck"
                    ngx.say("Nginx Worker PID: ", ngx.worker.pid())
                    ngx.print(hc.status_page())
                }
    }
}
