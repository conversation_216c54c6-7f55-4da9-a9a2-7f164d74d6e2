# Tomcat 构建配置文件

# Harbor 仓库配置
HARBOR_REGISTRY_DEFAULT="192.168.200.39:1443"
HARBOR_USER_DEFAULT=""
HARBOR_PASSWORD_DEFAULT=""

# 默认版本配置
DEFAULT_JDK_VERSION="8"
DEFAULT_TOMCAT_VERSION="9.0.100"
DEFAULT_VARIANT="crypto"

# 构建配置
BUILD_DEFAULTS=(
    NAMESPACE="btit"
    REPOSITORY="infra/tomcat"
    ARCH="amd64"
)

# 支持的版本
SUPPORTED_JDK_VERSIONS=("8" "17")
SUPPORTED_TOMCAT_VERSIONS=("9.0.100")
SUPPORTED_VARIANTS=("crypto" "default")
SUPPORTED_ARCHITECTURES=("amd64" "arm64")

# 路径配置
CONFIG_PATHS=(
    TOMCAT_VERSIONS_FILE="versions/tomcat_versions.json"
    JDK_VERSIONS_FILE="versions/jdk_versions.json"
    TEMPLATE_FILE="templates/Dockerfile.template"
    HARBOR_CONFIG="config/harbor.conf"
)

# 下载配置
DOWNLOAD_MIRRORS=(
    "https://dlcdn.apache.org"
    "https://archive.apache.org/dist"
    "https://downloads.apache.org/tomcat"
    "https://mirrors.tuna.tsinghua.edu.cn/apache"
    "https://mirrors.aliyun.com/apache"
    "https://mirrors.huaweicloud.com/apache"
)

# 重试配置
RETRY_CONFIG=(
    MAX_RETRIES=3
    RETRY_DELAY=5
    CONNECT_TIMEOUT=10
)

# 日志配置
LOG_CONFIG=(
    ENABLE_DEBUG=false
    LOG_LEVEL="INFO"
    LOG_FILE="logs/build-tomcat.log"
) 