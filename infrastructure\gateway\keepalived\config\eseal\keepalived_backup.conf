! Configuration File for keepalived - 电子签章系统备节点

global_defs {
   router_id LVS_ESEAL_BACKUP
   enable_script_security  # 启用脚本安全性
   script_user root        # 脚本以root用户执行
}

vrrp_script check_redis {
   script "/etc/keepalived/scripts/eseal/check_redis.sh"
   interval 5
}

vrrp_script check_mysql {
   script "/etc/keepalived/scripts/eseal/check_mysql.sh"
   interval 5
}

#vrrp_script check_nginx {
#   script "/etc/keepalived/scripts/eseal/check_nginx.sh"
#   interval 5
#}

vrrp_instance VI_ESEAL {
    state BACKUP                # 所有节点初始状态为BACKUP

    # 启用非抢占模式
    nopreempt

    interface ens192             # 网络接口，根据实际环境修改
    virtual_router_id 51        # 虚拟路由ID，集群中必须唯一

    # 优先级设置 - 备节点基础优先级
    priority 100                # 备节点基础优先级

    advert_int 1                # VRRP通告间隔，单位是秒

    # 认证设置
    authentication {
        auth_type PASS
        auth_pass KeepAl1v     # 密码，集群节点间必须一致
    }

    # 健康检查配置 - 监控Redis和Nginx服务
    track_script {
        check_redis             # 跟踪Redis服务状态
        check_mysql             # 跟踪MySQL服务状态
        #check_nginx            # 跟踪Nginx服务状态 - 暂时禁用
    }

    # 虚拟IP地址设置
    virtual_ipaddress {
        ***************/24        # 服务的虚拟IP
    }

    # 单播配置，适用于不同子网间通信
    unicast_src_ip *************** # 备节点IP
    unicast_peer {
        ***************            # 主节点IP
    }
    # 状态切换通知脚本 - 处理Redis主从切换
    notify_master "/etc/keepalived/scripts/eseal/notify.sh MASTER VI_ESEAL"
    notify_backup "/etc/keepalived/scripts/eseal/notify.sh BACKUP VI_ESEAL"
    notify_fault "/etc/keepalived/scripts/eseal/notify.sh FAULT VI_ESEAL"
} 