# 引入Lua共享字典，用于存储健康检查状态
lua_shared_dict healthcheck 10m;

upstream cloudkey_backend {
    # 定义后端服务器 - 使用被动健康检查
    server 192.168.200.137:8082;
    server 192.168.200.138:8082;
    
    # 负载均衡优化
    # 使用默认的轮询(round-robin)策略
    keepalive 32;                    # 保持连接数
    keepalive_requests 100;          # 每个连接的请求数
    keepalive_timeout 60s;           # 连接超时时间
    
    # 健康检查
    zone backend_cloudkey 64k;       # 定义共享内存区域用于健康检查
}

# 初始化健康检查
init_worker_by_lua_file lua/init_worker.lua;

server {
    listen                  6443 ssl;
    server_name             in_ssl;
    keepalive_timeout       70;

    # SSL配置优化
    ssl_certificate         /etc/nginx/ssl/nginx.cer;
    ssl_certificate_key     /etc/nginx/ssl/nginx_cert.key;
    ssl_protocols           TLSv1.2 TLSv1.3;
    ssl_ciphers             HIGH:!aNULL:!MD5:!3DES;
    ssl_prefer_server_ciphers on;
    ssl_session_cache       shared:SSL:10m;
    ssl_session_timeout     10m;
    ssl_verify_client       off;
    ssl_stapling            on;
    ssl_stapling_verify     on;
    
    # 日志配置 - 减少日志级别，提高性能
    access_log              /var/log/nginx/access_cloudkey_6443.log;
    error_log               /var/log/nginx/error_cloudkey_6443.log error;

    # 缓冲配置优化
    client_header_buffer_size       4k;
    large_client_header_buffers     4 1m;
    client_max_body_size            100m;
    client_body_buffer_size         128k;

    # 安全相关的HTTP头
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # 全局CORS配置（允许跨域访问）- 在生产环境中建议限制为特定域名
    add_header Access-Control-Allow-Origin "*" always;
    add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
    add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization" always;
    add_header Access-Control-Allow-Credentials "true" always;
    add_header Access-Control-Max-Age "3600" always;
    
    # GZIP压缩配置
    gzip on;
    gzip_comp_level 6;
    gzip_min_length 1k;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    gzip_vary on;
    
    # 处理OPTIONS请求（预检请求）
    if ($request_method = 'OPTIONS') {
        return 204;
    }

    location /cloudkeyserver/ {
        # 使用Lua智能路由，根据健康状态选择后端服务器
        rewrite_by_lua_file lua/router.lua;
        
        # 使用balancer_by_lua代替proxy_pass
        balancer_by_lua_file lua/balancer.lua;
        
        # 代理头部设置
        proxy_set_header Host      $host:$server_port;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # 错误处理
        proxy_intercept_errors on;
        
        # 缓存设置
        proxy_cache_bypass $http_cache_control;
        proxy_cache_valid 200 302 10m;
        proxy_cache_valid 404 1m;
        
        # 代理缓冲设置
        proxy_buffer_size 16k;
        proxy_buffers 4 32k;
        proxy_busy_buffers_size 64k;
        
        # 连接升级支持（WebSocket）
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    
    # 健康检查状态查询接口
    location /status {
        default_type application/json;
        content_by_lua_block {
            local cjson = require "cjson.safe"
            local healthcheck = package.loaded.my_checker
            
            if not healthcheck then
                ngx.status = 500
                ngx.say(cjson.encode({
                    success = false,
                    message = "健康检查模块未初始化"
                }))
                return
            end
            
            local peers = healthcheck.get_all_peers()
            
            ngx.say(cjson.encode({
                success = true,
                data = peers
            }))
        }
        
        # 限制只允许内部网络访问
        allow 127.0.0.1;
        allow ***********/16;
        deny all;
    }
    
    # 服务器健康检查测试接口
    location /check {
        default_type application/json;
        content_by_lua_block {
            local health_check = require "health_check"
            local cjson = require "cjson.safe"
            
            -- 从请求参数中获取目标
            local target = ngx.req.get_uri_args()["target"]
            local ip, port
            
            if not target then
                -- 从健康检查实例中获取所有服务器
                local healthcheck = package.loaded.my_checker
                local peers = healthcheck and healthcheck.get_all_peers() or {}
                
                if #peers > 0 then
                    -- 优先选择健康的服务器
                    local healthy_found = false
                    for _, peer in ipairs(peers) do
                        if peer.healthy then
                            ip = peer.ip
                            port = peer.port
                            healthy_found = true
                            break
                        end
                    end
                    
                    -- 如果没有健康的服务器，使用第一个服务器
                    if not healthy_found then
                        ip = peers[1].ip
                        port = peers[1].port
                    end
                else
                    -- 如果无法从健康检查获取，则直接从共享内存中获取
                    local upstreams = ngx.shared.healthcheck:get("upstreams")
                    if upstreams then
                        local upstream_servers = cjson.decode(upstreams)
                        if #upstream_servers > 0 then
                            ip = upstream_servers[1].ip
                            port = upstream_servers[1].port
                        else
                            ngx.status = 400
                            ngx.say(health_check.format_response(
                                false, 
                                "未指定target参数，且无法确定默认服务器"
                            ))
                            return
                        end
                    else
                        ngx.status = 400
                        ngx.say(health_check.format_response(
                            false, 
                            "未指定target参数，且无法确定默认服务器"
                        ))
                        return
                    end
                end
            else
                -- 解析用户提供的目标参数
                local parts = {}
                for part in string.gmatch(target, "[^:]+") do
                    table.insert(parts, part)
                end
                
                ip = parts[1]
                port = tonumber(parts[2]) or 8082
            end
            
            -- 执行健康检查
            local success, result = health_check.check_server(
                ip, 
                port, 
                "/cloudkeyserver/system/setting/version", 
                2000
            )
            
            -- 返回检查结果
            ngx.status = success and 200 or 503
            ngx.say(health_check.format_response(
                success,
                success and result or "服务器连接失败: " .. (result or "未知错误"),
                { ip = ip, port = port }
            ))
        }
        
        # 限制只允许内部网络访问
        allow 127.0.0.1;
        allow ***********/16;
        deny all;
    }
    
    # 健康检查端点
    location /health {
        access_log off;
        return 200 "ok";
    }
    
    # 错误页面配置
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
} 